from django.core.management.base import BaseCommand
from django.utils.text import slugify
from main.models import County, Lake, Video
from decimal import Decimal
import random

class Command(BaseCommand):
    help = 'Populate database with comprehensive fishing location data for Romania'

    def handle(self, *args, **kwargs):
        self.stdout.write('Creating comprehensive fishing location data for Romania...')
        
        # Create all 42 Romanian counties
        self.create_counties()
        
        # Create fishing locations for each county
        self.create_fishing_locations()

        # Create sample fishing tutorial videos
        self.create_tutorial_videos()

        self.stdout.write(self.style.SUCCESS('Successfully populated fishing location data'))

    def create_counties(self):
        """Create all 42 Romanian counties with proper regions"""
        counties_data = [
            # Moldova Region
            {'name': 'Bacău', 'region': 'MOLDOVA'},
            {'name': 'Botoșani', 'region': 'MOLDOVA'},
            {'name': 'Iași', 'region': 'MOLDOVA'},
            {'name': 'Neamț', 'region': 'MOLDOVA'},
            {'name': 'Suceava', 'region': 'MOLDOVA'},
            {'name': 'Vaslui', 'region': 'MOLDOVA'},
            
            # Muntenia Region
            {'name': 'Argeș', 'region': 'MUNTENIA'},
            {'name': 'Buzău', 'region': 'MUNTENIA'},
            {'name': 'Călărași', 'region': 'MUNTENIA'},
            {'name': 'Dâmbovița', 'region': 'MUNTENIA'},
            {'name': 'Giurgiu', 'region': 'MUNTENIA'},
            {'name': 'Ialomița', 'region': 'MUNTENIA'},
            {'name': 'Ilfov', 'region': 'MUNTENIA'},
            {'name': 'Prahova', 'region': 'MUNTENIA'},
            {'name': 'Teleorman', 'region': 'MUNTENIA'},
            
            # Oltenia Region
            {'name': 'Dolj', 'region': 'OLTENIA'},
            {'name': 'Gorj', 'region': 'OLTENIA'},
            {'name': 'Mehedinți', 'region': 'OLTENIA'},
            {'name': 'Olt', 'region': 'OLTENIA'},
            {'name': 'Vâlcea', 'region': 'OLTENIA'},
            
            # Banat Region
            {'name': 'Caraș-Severin', 'region': 'BANAT'},
            {'name': 'Timiș', 'region': 'BANAT'},
            
            # Crișana Region
            {'name': 'Arad', 'region': 'CRISANA'},
            {'name': 'Bihor', 'region': 'CRISANA'},
            {'name': 'Sălaj', 'region': 'CRISANA'},
            
            # Maramureș Region
            {'name': 'Maramureș', 'region': 'MARAMURES'},
            
            # Transilvania Region
            {'name': 'Alba', 'region': 'TRANSILVANIA'},
            {'name': 'Brașov', 'region': 'TRANSILVANIA'},
            {'name': 'Cluj', 'region': 'TRANSILVANIA'},
            {'name': 'Covasna', 'region': 'TRANSILVANIA'},
            {'name': 'Harghita', 'region': 'TRANSILVANIA'},
            {'name': 'Hunedoara', 'region': 'TRANSILVANIA'},
            {'name': 'Mureș', 'region': 'TRANSILVANIA'},
            {'name': 'Sibiu', 'region': 'TRANSILVANIA'},
            {'name': 'Bistrița-Năsăud', 'region': 'TRANSILVANIA'},
            
            # Dobrogea Region
            {'name': 'Constanța', 'region': 'DOBROGEA'},
            {'name': 'Tulcea', 'region': 'DOBROGEA'},
            {'name': 'Galați', 'region': 'DOBROGEA'},
            {'name': 'Vrancea', 'region': 'DOBROGEA'},
            {'name': 'Brăila', 'region': 'DOBROGEA'},
            
            # București
            {'name': 'București', 'region': 'BUCURESTI'},
        ]
        
        for county_data in counties_data:
            county, created = County.objects.get_or_create(
                name=county_data['name'],
                defaults={
                    'region': county_data['region'],
                    'slug': slugify(county_data['name'])
                }
            )
            if created:
                self.stdout.write(f'Created county: {county.name}')
            else:
                self.stdout.write(f'County already exists: {county.name}')

    def create_fishing_locations(self):
        """Create fishing locations for each county"""
        # Common fish species in Romanian waters
        fish_species = [
            'Crap', 'Caras', 'Șalău', 'Știucă', 'Biban', 'Clean', 'Amur', 
            'Somn', 'Plătică', 'Roșioară', 'Babușcă', 'Linul', 'Morun'
        ]
        
        # Common facilities at fishing locations
        facilities = [
            'parcare', 'toalete', 'restaurant', 'cazare', 'chioșc', 
            'închiriere echipamente', 'pontoane', 'bărci', 'grătar'
        ]
        
        # Get all counties
        counties = County.objects.all()
        
        for county in counties:
            # Create 3-5 fishing locations per county
            num_locations = random.randint(3, 5)
            
            for i in range(num_locations):
                lake_data = self.generate_lake_data(county, i + 1, fish_species, facilities)
                
                lake, created = Lake.objects.get_or_create(
                    name=lake_data['name'],
                    county=county,
                    defaults=lake_data
                )
                
                if created:
                    self.stdout.write(f'Created lake: {lake.name} in {county.name}')
                else:
                    self.stdout.write(f'Lake already exists: {lake.name}')

    def generate_lake_data(self, county, index, fish_species, facilities):
        """Generate realistic lake data for a county"""

        # Lake name patterns based on Romanian naming conventions
        lake_prefixes = ['Lacul', 'Balta', 'Iazul', 'Complexul Piscicol', 'Rezervația']
        lake_suffixes = [
            'Mare', 'Mic', 'Nou', 'Vechi', 'Central', 'de Sus', 'de Jos',
            'Verde', 'Albastru', 'Liniștit', 'Frumos', 'Pescăresc'
        ]

        # Generate lake name
        if index == 1:
            # First lake often named after the county or major city
            lake_name = f"Lacul {county.name}"
        else:
            prefix = random.choice(lake_prefixes)
            suffix = random.choice(lake_suffixes)
            lake_name = f"{prefix} {suffix} {county.name}"

        # Generate realistic coordinates within Romania's bounds
        # Romania's approximate bounds: lat 43.6-48.3, lng 20.2-29.7
        base_coords = self.get_county_coordinates(county.name)

        # Add small random offset for multiple lakes in same county
        lat_offset = random.uniform(-0.1, 0.1)
        lng_offset = random.uniform(-0.1, 0.1)

        latitude = Decimal(str(round(base_coords['lat'] + lat_offset, 6)))
        longitude = Decimal(str(round(base_coords['lng'] + lng_offset, 6)))

        # Generate fish types (3-6 species per lake)
        num_fish = random.randint(3, 6)
        selected_fish = random.sample(fish_species, num_fish)
        fish_types = ', '.join(selected_fish)

        # Generate facilities (2-5 facilities per lake)
        num_facilities = random.randint(2, 5)
        selected_facilities = random.sample(facilities, num_facilities)
        facilities_str = ' '.join(selected_facilities)

        # Generate realistic price (30-120 RON per day)
        price = Decimal(str(random.randint(30, 120)))

        # Generate description
        description = self.generate_description(lake_name, selected_fish, county.name)

        # Generate address
        address = f"Comuna {random.choice(['Centrală', 'Mare', 'Nouă', 'Veche'])}, Județul {county.name}"

        return {
            'name': lake_name,
            'description': description,
            'address': address,
            'latitude': latitude,
            'longitude': longitude,
            'fish_types': fish_types,
            'facilities': facilities_str,
            'price_per_day': price,
            'is_active': True
        }

    def generate_description(self, lake_name, fish_types, county_name):
        """Generate a realistic description for the lake"""
        descriptions = [
            f"{lake_name} este o destinație populară pentru pescarii din {county_name}. Lacul oferă condiții excelente pentru pescuitul la {', '.join(fish_types[:2])}.",
            f"Situat în județul {county_name}, {lake_name} este cunoscut pentru abundența de {fish_types[0].lower()} și {fish_types[1].lower()}. Locația oferă facilități moderne pentru pescari.",
            f"{lake_name} reprezintă una dintre cele mai apreciate zone de pescuit din {county_name}. Apele sunt populate cu {', '.join(fish_types[:3]).lower()}.",
            f"Complex piscicol modern în {county_name}, {lake_name} oferă pescuit de calitate în condiții civilizate. Speciile principale sunt {fish_types[0].lower()} și {fish_types[1].lower()}."
        ]

        return random.choice(descriptions)

    def get_county_coordinates(self, county_name):
        """Get approximate coordinates for each county center"""
        coordinates = {
            # Moldova Region
            'Bacău': {'lat': 46.5670, 'lng': 26.9146},
            'Botoșani': {'lat': 47.7475, 'lng': 26.6656},
            'Iași': {'lat': 47.1585, 'lng': 27.6014},
            'Neamț': {'lat': 46.9759, 'lng': 26.3819},
            'Suceava': {'lat': 47.6514, 'lng': 26.2551},
            'Vaslui': {'lat': 46.6407, 'lng': 27.7276},

            # Muntenia Region
            'Argeș': {'lat': 44.8565, 'lng': 24.8692},
            'Buzău': {'lat': 45.1500, 'lng': 26.8207},
            'Călărași': {'lat': 44.2026, 'lng': 27.3306},
            'Dâmbovița': {'lat': 44.9317, 'lng': 25.4571},
            'Giurgiu': {'lat': 43.9037, 'lng': 25.9699},
            'Ialomița': {'lat': 44.5648, 'lng': 27.3729},
            'Ilfov': {'lat': 44.5133, 'lng': 26.0996},
            'Prahova': {'lat': 45.1000, 'lng': 26.0167},
            'Teleorman': {'lat': 43.9859, 'lng': 25.3424},

            # Oltenia Region
            'Dolj': {'lat': 44.3302, 'lng': 23.7949},
            'Gorj': {'lat': 45.0485, 'lng': 23.2795},
            'Mehedinți': {'lat': 44.6319, 'lng': 22.6563},
            'Olt': {'lat': 44.4268, 'lng': 24.3681},
            'Vâlcea': {'lat': 45.1167, 'lng': 24.3667},

            # Banat Region
            'Caraș-Severin': {'lat': 45.3000, 'lng': 21.8833},
            'Timiș': {'lat': 45.7489, 'lng': 21.2087},

            # Crișana Region
            'Arad': {'lat': 46.1866, 'lng': 21.3123},
            'Bihor': {'lat': 47.0722, 'lng': 21.9211},
            'Sălaj': {'lat': 47.1925, 'lng': 23.0644},

            # Maramureș Region
            'Maramureș': {'lat': 47.6587, 'lng': 23.5885},

            # Transilvania Region
            'Alba': {'lat': 46.0667, 'lng': 23.5833},
            'Brașov': {'lat': 45.6427, 'lng': 25.5887},
            'Cluj': {'lat': 46.7712, 'lng': 23.6236},
            'Covasna': {'lat': 45.8667, 'lng': 26.1833},
            'Harghita': {'lat': 46.3667, 'lng': 25.8000},
            'Hunedoara': {'lat': 45.7494, 'lng': 22.9041},
            'Mureș': {'lat': 46.5425, 'lng': 24.5579},
            'Sibiu': {'lat': 45.7983, 'lng': 24.1256},
            'Bistrița-Năsăud': {'lat': 47.1333, 'lng': 24.5000},

            # Dobrogea Region
            'Constanța': {'lat': 44.1598, 'lng': 28.6348},
            'Tulcea': {'lat': 45.1667, 'lng': 28.8000},
            'Galați': {'lat': 45.4353, 'lng': 28.0080},
            'Vrancea': {'lat': 45.6979, 'lng': 27.1836},
            'Brăila': {'lat': 45.2692, 'lng': 27.9574},

            # București
            'București': {'lat': 44.4268, 'lng': 26.1025},
        }

        return coordinates.get(county_name, {'lat': 45.9432, 'lng': 24.9668})  # Default to Romania center

    def create_tutorial_videos(self):
        """Create sample fishing tutorial videos"""
        videos_data = [
            {
                'title': 'Pescuitul la crap - Tehnici de bază',
                'description': 'Învață tehnicile fundamentale pentru pescuitul la crap. Acest tutorial acoperă alegerea echipamentului, prepararea momelii și tehnicile de lansare.',
                'url': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'is_featured': True
            },
            {
                'title': 'Cum să alegi locul perfect pentru pescuit',
                'description': 'Ghid complet pentru identificarea celor mai bune locuri de pescuit. Învață să citești apa și să identifici zonele cu potențial ridicat.',
                'url': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'is_featured': True
            },
            {
                'title': 'Pescuitul la șalău - Strategii avansate',
                'description': 'Tehnici avansate pentru pescuitul la șalău. Acoperă alegerea momelii, timpul optim și tacticile de pescuit.',
                'url': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'is_featured': False
            },
            {
                'title': 'Echipamentul esențial pentru începători',
                'description': 'Tot ce trebuie să știi despre echipamentul de pescuit pentru începători. De la undițe la momeli și accesorii.',
                'url': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'is_featured': False
            },
            {
                'title': 'Pescuitul la știucă - Tehnici și trucuri',
                'description': 'Ghid specializat pentru pescuitul la știucă. Învață tehnicile specifice și secretele pescarilor experimentați.',
                'url': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'is_featured': True
            },
            {
                'title': 'Prepararea momelii naturale',
                'description': 'Cum să prepari și să păstrezi momeli naturale eficiente. Rețete și tehnici dovedite pentru succes.',
                'url': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'is_featured': False
            },
            {
                'title': 'Pescuitul de noapte - Ghid complet',
                'description': 'Totul despre pescuitul de noapte: echipament, siguranță, tehnici și cele mai bune locuri.',
                'url': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'is_featured': False
            },
            {
                'title': 'Pescuitul la feeder - Metoda modernă',
                'description': 'Introducere în pescuitul la feeder. Echipament specializat, tehnici și strategii pentru rezultate maxime.',
                'url': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'is_featured': False
            }
        ]

        for video_data in videos_data:
            video, created = Video.objects.get_or_create(
                title=video_data['title'],
                defaults=video_data
            )

            if created:
                self.stdout.write(f'Created video: {video.title}')
            else:
                self.stdout.write(f'Video already exists: {video.title}')
